Numexpr was initially written by <PERSON>, and extended to more
types by <PERSON>.

<PERSON><PERSON> contributed support for booleans and simple-precision
floating point types, efficient strided and unaligned array operations
and multi-threading code.

<PERSON> contributed support for strings.

<PERSON> implemented the support for Intel VML (Vector Math
Library).

<PERSON> added support for the new iterator in NumPy, which allows
for better performance in more scenarios (like broadcasting,
fortran-ordered or non-native byte orderings).

<PERSON><PERSON><PERSON><PERSON> contributed important bug fixes and speed
enhancements.

<PERSON> contributed the port to Python 3.

Google Inc. contributed bug fixes.

<PERSON> improved readability of the Readme.

Robert A<PERSON> contributed bug fixes and ported the documentation to
numexpr.readthedocs.io. He has served as the maintainer of the package
since 2016 to 2023.

<PERSON><PERSON> fixed many bugs, and in particular, contributed valuable fixes
to the new regex sanitizer for expressions.
