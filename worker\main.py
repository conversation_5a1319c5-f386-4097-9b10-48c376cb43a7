import datetime
import time
import s3fs
import argparse
import threading
import requests

from himawari_processor import available_composites
from task_manager import TaskManager
from sync import HimawariDataSync
from utils import logger, _available_latest_time, generate_worker_id
from config import server_url


def check_files_available(target_time):
    """Check if 160 files are available for the given time"""
    try:
        fs = s3fs.S3FileSystem(anon=True)
        s3_path = 'noaa-himawari9/AHI-L1b-FLDK/{}'.format(target_time.strftime('%Y/%m/%d/%H%M'))

        files = fs.ls(s3_path)
        file_count = len(files)

        logger.info(f"Found {file_count} files for time {target_time.strftime('%Y-%m-%d %H:%M')} UTC")

        return file_count >= 160
    except Exception as e:
        logger.error(f"Error checking files for time {target_time.strftime('%Y-%m-%d %H:%M')} UTC: {e}")
        return False

def task_generator_thread(server_url):
    """Task generator thread that monitors data availability and creates tasks"""
    logger.info("Starting Himawari task generator thread...")

    current_target_time = None

    while True:
        try:
            # Get the latest available time
            latest_time = _available_latest_time()

            # If we don't have a current target time, set it to the latest time
            if current_target_time is None:
                current_target_time = latest_time

            # If the current target time is still in the future compared to latest available, wait
            if current_target_time > latest_time:
                time.sleep(60)
                continue

            # Check if files are available
            if check_files_available(current_target_time):
                for composite_name in available_composites:
                    try:
                        # Create task on server (server will handle deduplication)
                        response = requests.post(
                            f"{server_url}/api/tasks",
                            json={
                                'composite': composite_name,
                                'timestamp': current_target_time.isoformat(),
                                'priority': 'normal'
                            },
                            timeout=10
                        )
                        if response.status_code == 201:
                            task_data = response.json()
                            task_id = task_data['task_id']
                            logger.info(f"Created task {task_id} for {composite_name} at {current_target_time.strftime('%Y-%m-%d %H:%M')} UTC")
                        else:
                            logger.error(f"Failed to create task for {composite_name}: {response.status_code} {response.text}")
                    except Exception as e:
                        logger.error(f"Error creating task for {composite_name}: {e}")

                # Move to next 10-minute interval
                current_target_time = current_target_time + datetime.timedelta(minutes=10)
            else:
                logger.info(f"Data not complete for time {current_target_time.strftime('%Y-%m-%d %H:%M')} UTC, waiting...")

            # Wait 1 minute before next check
            logger.info("Waiting 1 minute before next check...")
            time.sleep(60)

        except KeyboardInterrupt:
            logger.info("Task generator received interrupt signal, shutting down...")
            break
        except Exception as e:
            logger.error(f"Unexpected error in task generator: {e}")
            logger.info("Waiting 1 minute before retrying...")
            time.sleep(60)


def himawari_sync_thread():
    """Data synchronization thread"""
    logger.info("Starting Himawari data sync thread...")
    try:
        syncer = HimawariDataSync()
        syncer.run()
    except Exception as e:
        logger.error(f"Error in sync thread: {e}")


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Himawari satellite data processing')
    parser.add_argument('--task', action='store_true',
                        help='Enable task generator that monitors data availability and creates tasks')
    parser.add_argument('--sync', action='store_true',
                        help='Enable Himawari data synchronization from NOAA S3')
    parser.add_argument('--worker', action='store_true',
                        help='Enable composite worker that processes tasks from the queue')
    parser.add_argument('--worker-id', help='Worker ID (auto-generated if not provided)')

    args = parser.parse_args()

    # Generate worker ID if not provided
    worker_id = args.worker_id or generate_worker_id()
    logger.info(f"Worker ID: {worker_id}")

    # Track active threads
    active_threads = []

    if args.task:
        # Start task generator thread
        generator_thread = threading.Thread(
            target=task_generator_thread,
            args=(server_url,),
            daemon=False  # Don't use daemon threads for standalone operation
        )
        generator_thread.start()
        active_threads.append(generator_thread)
        logger.info("Task generator thread started")

    if args.sync:
        # Start data synchronization thread
        sync_thread = threading.Thread(
            target=himawari_sync_thread,
            daemon=False  # Don't use daemon threads for standalone operation
        )
        sync_thread.start()
        active_threads.append(sync_thread)
        logger.info("Data sync thread started")

    # Determine if we should start the worker (TaskManager)
    start_worker = args.worker or (not args.task and not args.sync)

    if start_worker:
        # Start task manager
        logger.info("Starting task manager...")
        task_manager = TaskManager(
            server_url=server_url,
            worker_id=worker_id,
            poll_interval=5
        )

        if active_threads:
            # If we have other threads running, start TaskManager in a separate thread
            worker_thread = threading.Thread(
                target=task_manager.start,
                daemon=False
            )
            worker_thread.start()
            active_threads.append(worker_thread)
            logger.info("Task manager thread started")
        else:
            # If TaskManager is the only component, run it in main thread
            task_manager.start()

    # If we have threads running, wait for them
    if active_threads:
        try:
            logger.info(f"Running with {len(active_threads)} active threads. Press Ctrl+C to stop.")
            for thread in active_threads:
                thread.join()
        except KeyboardInterrupt:
            logger.info("Received interrupt signal, shutting down...")
            # Threads will handle their own cleanup


if __name__ == '__main__':
    main()